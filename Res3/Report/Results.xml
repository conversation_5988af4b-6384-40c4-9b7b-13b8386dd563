<?xml version="1.0"?>
<!DOCTYPE Report
[
<!ELEMENT Report (General ,(Doc|BPT)) >
<!ATTLIST Report ver CDATA #REQUIRED tmZone CDATA #REQUIRED>

<!ELEMENT General ( DocLocation ) >
<!ATTLIST General productName CDATA #REQUIRED productVer CDATA #REQUIRED os CDATA #REQUIRED host CDATA #REQUIRED>

<!ELEMENT BPT (DName,Res,DVer?,TSet?,TInst?,NodeArgs,AdditionalInfo*,Doc*) >
<!ATTLIST BPT rID ID #REQUIRED >

<!ELEMENT Doc (DName,Res,DVer?,TSet?,TInst?,RunType?,DT?,AdditionalInfo*,Step*,DIter*,Step*,Action*,Summary?,TestMaintenanceSummary*,NodeArgs?) >
<!ATTLIST Doc rID ID #REQUIRED type (Test|BC) "Test" productName CDATA #REQUIRED BCIter CDATA #IMPLIED >

<!ELEMENT RunType ( #PCDATA )>
<!ATTLIST RunType fmStep (False|True) "False" batch (False|True) "False" upDesc (False|True) "False" upChk (False|True) "False" upAS (False|True) "False">

<!ELEMENT DName ( #PCDATA ) >

<!ELEMENT Res ( #PCDATA ) >

<!ELEMENT AdditionalInfo (AdditionalDataName,AdditionalDataValue ) >

<!ELEMENT AdditionalDataName ( #PCDATA ) >

<!ELEMENT AdditionalDataValue ( #PCDATA ) >

<!ELEMENT DVer ( #PCDATA ) >

<!ELEMENT TSet ( #PCDATA ) >

<!ELEMENT TInst ( #PCDATA ) >

<!ELEMENT DIter (Step?,Action+,Summary?,NodeArgs)>
<!ATTLIST DIter rID ID #REQUIRED iterID CDATA #REQUIRED>

<!ELEMENT DocLocation ( #PCDATA )>

<!ELEMENT Action (AName,AIter*,(Step|HtmlStep|Action)*,Summary,ActionMaintenanceSummary*,NodeArgs ) >
<!ATTLIST Action rID ID #REQUIRED>

<!ELEMENT AIter ((Step|HtmlStep|Action)*,Summary?,NodeArgs) >
<!ATTLIST AIter rID ID #REQUIRED iterID CDATA #REQUIRED>

<!ELEMENT AName ( #PCDATA ) >

<!ELEMENT TestMaintenanceSummary (ActionMaintenanceSummary) >
<!ATTLIST TestMaintenanceSummary ObjectsAdded CDATA #REQUIRED ObjectsUpdated CDATA #REQUIRED StepsUpdated CDATA #REQUIRED StepsComments CDATA #REQUIRED><!ELEMENT ActionMaintenanceSummary (ObjectChange* ) >
<!ATTLIST ActionMaintenanceSummary Action CDATA #REQUIRED Objects CDATA #REQUIRED Updated CDATA #REQUIRED LinesTotal CDATA #REQUIRED Added CDATA #REQUIRED LinesUpdated CDATA #REQUIRED>

<!ELEMENT ObjectChange  (Hierarchy, PropertyChangeList* ) >
<!ATTLIST ObjectChange Operation CDATA #IMPLIED OriginalRepository CDATA #IMPLIED>

<!ELEMENT PropertyChangeList (PropertyDef) >

<!ELEMENT PropertyDef (OriginalValue, NewValue) >
<!ATTLIST PropertyDef PropName CDATA #REQUIRED OriginRegularExpression (True|False) "False" NewRegularExpression (True|False) "False" >

<!ELEMENT OriginalValue ( #PCDATA )>

<!ELEMENT NewValue ( #PCDATA )>

<!ELEMENT Hierarchy  (ObjectName, Hierarchy*) >
<!ATTLIST Hierarchy MicClass CDATA #REQUIRED>

<!ELEMENT ObjectName  ( #PCDATA ) >

<!ELEMENT Step (Obj,Details,Time,(Step|HtmlStep|Doc)*,NodeArgs) >
<!ATTLIST Step rID ID #REQUIRED retval CDATA #IMPLIED>

<!ELEMENT HtmlStep (HTML,(Step|HtmlStep|Doc)*,NodeArgs) >
<!ATTLIST HtmlStep rID ID #REQUIRED >

<!ELEMENT Obj ( #PCDATA ) >
<!ATTLIST Obj plainTxt (False|True) "True">

<!ELEMENT Details ( #PCDATA ) >
<!ATTLIST Details plainTxt (False|True) "True">

<!ELEMENT Time ( #PCDATA ) >

<!ELEMENT HTML ( #PCDATA ) >

<!ELEMENT Disp ( #PCDATA ) >

<!ELEMENT NodeArgs (Disp,TopPane?,BtmPane?)>
<!ATTLIST NodeArgs eType CDATA #REQUIRED icon CDATA #REQUIRED nRep CDATA #REQUIRED filter (False|True) "True">
<!ATTLIST NodeArgs status (Passed|Failed|Done|Warning|Information) "Done">
<!ATTLIST NodeArgs iconSel CDATA #IMPLIED nType CDATA #IMPLIED MovieMarker CDATA "">

<!ELEMENT TopPane (Path)>

<!ELEMENT BtmPane ( (Path|WR)?,ASHilite?)>
<!ATTLIST BtmPane vType CDATA "HTML">

<!ELEMENT Path ( #PCDATA ) >

<!ELEMENT ASHilite ( #PCDATA ) >

<!ELEMENT WR ( #PCDATA ) >

<!ELEMENT DT (NodeArgs) >
<!ATTLIST DT rID ID #REQUIRED>

<!ELEMENT Summary (Param*)>
<!ATTLIST Summary sTime CDATA #IMPLIED eTime CDATA #IMPLIED passed CDATA #IMPLIED failed CDATA #IMPLIED warnings CDATA #IMPLIED retval CDATA #IMPLIED stopped (False|True) "False" >

<!ELEMENT Param (ParamName,ParamVal)+ >
<!ATTLIST Param paramInOut (In|Out) "In">

<!ELEMENT ParamName ( #PCDATA ) >

<!ELEMENT ParamVal ( #PCDATA ) >

]
>
<Report ver="2.0" tmZone="中国标准时间">
<General productName="QuickTest Professional" productVer="9.5" os="" host="PC-038"><DocLocation><![CDATA[C:\Program Files (x86)\HP\QuickTest Professional\Tests\Test1]]></DocLocation></General>
<Doc rID="T1"   productName= "QuickTest Professional"  >
<DName><![CDATA[Test1]]></DName>
<Res><![CDATA[Res3]]></Res>
<DT rID="T2">
<NodeArgs eType="Table" icon="2" nRep="4" filter="False" >
<Disp><![CDATA[Run-Time Data Table]]></Disp>
<BtmPane vType="Table">
<Path><![CDATA[Default.xls]]></Path>
</BtmPane>
</NodeArgs>
</DT>
<DIter rID="T3" iterID="1" >
<Action rID="T4">
<AName><![CDATA[MainAction]]></AName>
<Action rID="T5">
<AName><![CDATA[LoginAction]]></AName>
<Step rID="T6">
<Obj plainTxt="False" ><![CDATA[Login]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:49]]></Time>
<Step rID="T7">
<Obj plainTxt="False" ><![CDATA[Agent Name:.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:49]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="11" >
<Disp><![CDATA[Agent Name:.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T8">
<Obj plainTxt="False" ><![CDATA[Password:.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA["mercury"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:50]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="13" >
<Disp><![CDATA[Password:.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T9">
<Obj plainTxt="False" ><![CDATA[OK.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:52]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="15" >
<Disp><![CDATA[OK.Click]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="10" >
<Disp><![CDATA[Login]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 9:35:48" eTime="2024/6/18/周二 - 9:35:52" passed="0" failed="0" warnings="0" ><Param ><ParamName><![CDATA[UserName]]></ParamName><ParamVal><![CDATA[user01]]></ParamVal>
</Param>
</Summary>
<NodeArgs eType="StartAction" icon="4" nRep="8" >
<Disp><![CDATA[LoginAction Summary]]></Disp>
</NodeArgs>
</Action>
<Action rID="T10">
<AName><![CDATA[OrderAction]]></AName>
<Step rID="T11">
<Obj plainTxt="False" ><![CDATA[Flight Reservation]]></Obj>
<Details plainTxt="False" ><![CDATA[Window]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:55]]></Time>
<Step rID="T12">
<Obj plainTxt="False" ><![CDATA[MaskEdBox.Type]]></Obj>
<Details plainTxt="False" ><![CDATA["121224"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:55]]></Time>
<NodeArgs eType="Replay" icon="9" nRep="20" >
<Disp><![CDATA[MaskEdBox.Type]]></Disp>
</NodeArgs>
</Step>
<Step rID="T13">
<Obj plainTxt="False" ><![CDATA[Fly From:.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["Frankfurt"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:55]]></Time>
<NodeArgs eType="Replay" icon="10" nRep="22" >
<Disp><![CDATA[Fly From:.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T14">
<Obj plainTxt="False" ><![CDATA[Fly To:.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["Paris"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:55]]></Time>
<NodeArgs eType="Replay" icon="10" nRep="24" >
<Disp><![CDATA[Fly To:.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T15">
<Obj plainTxt="False" ><![CDATA[FLIGHT.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:55]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="26" >
<Disp><![CDATA[FLIGHT.Click]]></Disp>
</NodeArgs>
</Step>
<Step rID="T16">
<Obj plainTxt="False" ><![CDATA[Flights Table]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:55]]></Time>
<Step rID="T17">
<Obj plainTxt="False" ><![CDATA[From.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["11278   FRA   05:58 PM   PAR   07:53 PM   AA     $174.00"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:55]]></Time>
<NodeArgs eType="Replay" icon="11" nRep="29" >
<Disp><![CDATA[From.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T18">
<Obj plainTxt="False" ><![CDATA[OK.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:55]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="31" >
<Disp><![CDATA[OK.Click]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="28" >
<Disp><![CDATA[Flights Table]]></Disp>
</NodeArgs>
</Step>
<Step rID="T19">
<Obj plainTxt="False" ><![CDATA[Name:.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA["nzy"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:56]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="33" >
<Disp><![CDATA[Name:.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T20">
<Obj plainTxt="False" ><![CDATA[First.Set]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:57]]></Time>
<NodeArgs eType="Replay" icon="12" nRep="35" >
<Disp><![CDATA[First.Set]]></Disp>
</NodeArgs>
</Step>
<Step rID="T21">
<Obj plainTxt="False" ><![CDATA[Tickets:.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA["10"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:35:58]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="37" >
<Disp><![CDATA[Tickets:.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T22">
<Obj plainTxt="False" ><![CDATA[Insert Order.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:00]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="39" >
<Disp><![CDATA[Insert Order.Click]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="8" nRep="19" >
<Disp><![CDATA[Flight Reservation]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 9:35:52" eTime="2024/6/18/周二 - 9:36:00" passed="0" failed="0" warnings="0" ></Summary>
<NodeArgs eType="StartAction" icon="4" nRep="17" >
<Disp><![CDATA[OrderAction Summary]]></Disp>
</NodeArgs>
</Action>
<Action rID="T23">
<AName><![CDATA[SearchAction]]></AName>
<Step rID="T24">
<Obj plainTxt="False" ><![CDATA[Flight Reservation]]></Obj>
<Details plainTxt="False" ><![CDATA[Window]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:00]]></Time>
<Step rID="T25">
<Obj plainTxt="False" ><![CDATA[Menu.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["File;Open Order..."]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:00]]></Time>
<NodeArgs eType="Replay" icon="13" nRep="44" >
<Disp><![CDATA[Menu.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T26">
<Obj plainTxt="False" ><![CDATA[Open Order]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:07]]></Time>
<Step rID="T27">
<Obj plainTxt="False" ><![CDATA[Customer Name.Set]]></Obj>
<Details plainTxt="False" ><![CDATA["ON"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:07]]></Time>
<NodeArgs eType="Replay" icon="14" nRep="47" >
<Disp><![CDATA[Customer Name.Set]]></Disp>
</NodeArgs>
</Step>
<Step rID="T28">
<Obj plainTxt="False" ><![CDATA[Edit.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA["nzy"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:08]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="49" >
<Disp><![CDATA[Edit.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T29">
<Obj plainTxt="False" ><![CDATA[OK.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:09]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="51" >
<Disp><![CDATA[OK.Click]]></Disp>
</NodeArgs>
</Step>
<Step rID="T30">
<Obj plainTxt="False" ><![CDATA[Search Results]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:10]]></Time>
<Step rID="T31">
<Obj plainTxt="False" ><![CDATA[Flight No..Click]]></Obj>
<Details plainTxt="False" ><![CDATA[168, 1]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:10]]></Time>
<NodeArgs eType="Replay" icon="11" nRep="54" >
<Disp><![CDATA[Flight No..Click]]></Disp>
</NodeArgs>
</Step>
<Step rID="T32">
<Obj plainTxt="False" ><![CDATA[OK.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:11]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="56" >
<Disp><![CDATA[OK.Click]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="53" >
<Disp><![CDATA[Search Results]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="46" >
<Disp><![CDATA[Open Order]]></Disp>
</NodeArgs>
</Step>
<Step rID="T33">
<Obj plainTxt="False" ><![CDATA[Menu.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["File;Open Order..."]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:12]]></Time>
<NodeArgs eType="Replay" icon="13" nRep="58" >
<Disp><![CDATA[Menu.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T34">
<Obj plainTxt="False" ><![CDATA[Open Order]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:12]]></Time>
<Step rID="T35">
<Obj plainTxt="False" ><![CDATA[Flight Date.Set]]></Obj>
<Details plainTxt="False" ><![CDATA["ON"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:12]]></Time>
<NodeArgs eType="Replay" icon="14" nRep="61" >
<Disp><![CDATA[Flight Date.Set]]></Disp>
</NodeArgs>
</Step>
<Step rID="T36">
<Obj plainTxt="False" ><![CDATA[MaskEdBox.Type]]></Obj>
<Details plainTxt="False" ><![CDATA["121224"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<NodeArgs eType="Replay" icon="9" nRep="63" >
<Disp><![CDATA[MaskEdBox.Type]]></Disp>
</NodeArgs>
</Step>
<Step rID="T37">
<Obj plainTxt="False" ><![CDATA[OK.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="65" >
<Disp><![CDATA[OK.Click]]></Disp>
</NodeArgs>
</Step>
<Step rID="T38">
<Obj plainTxt="False" ><![CDATA[Search Results]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<Step rID="T39">
<Obj plainTxt="False" ><![CDATA[Flight No..Select]]></Obj>
<Details plainTxt="False" ><![CDATA["nzy                     11        AA        11278         1   10 174.000012/12/2024     Tuesday  FRA                     Frankfurt  05:58 PM  PAR                         Paris  07:53 PM"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<NodeArgs eType="Replay" icon="11" nRep="68" >
<Disp><![CDATA[Flight No..Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T40">
<Obj plainTxt="False" ><![CDATA[OK.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="70" >
<Disp><![CDATA[OK.Click]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="67" >
<Disp><![CDATA[Search Results]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="60" >
<Disp><![CDATA[Open Order]]></Disp>
</NodeArgs>
</Step>
<Step rID="T41">
<Obj plainTxt="False" ><![CDATA[Menu.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["File;Open Order..."]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<NodeArgs eType="Replay" icon="13" nRep="72" >
<Disp><![CDATA[Menu.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T42">
<Obj plainTxt="False" ><![CDATA[Open Order]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<Step rID="T43">
<Obj plainTxt="False" ><![CDATA[Order No..Set]]></Obj>
<Details plainTxt="False" ><![CDATA["ON"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<NodeArgs eType="Replay" icon="14" nRep="75" >
<Disp><![CDATA[Order No..Set]]></Disp>
</NodeArgs>
</Step>
<Step rID="T44">
<Obj plainTxt="False" ><![CDATA[Edit_2.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA["11"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="77" >
<Disp><![CDATA[Edit_2.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T45">
<Obj plainTxt="False" ><![CDATA[OK.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="79" >
<Disp><![CDATA[OK.Click]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="74" >
<Disp><![CDATA[Open Order]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="8" nRep="43" >
<Disp><![CDATA[Flight Reservation]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 9:36:00" eTime="2024/6/18/周二 - 9:36:13" passed="0" failed="0" warnings="0" ></Summary>
<NodeArgs eType="StartAction" icon="4" nRep="41" >
<Disp><![CDATA[SearchAction Summary]]></Disp>
</NodeArgs>
</Action>
<Action rID="T46">
<AName><![CDATA[ExitAction]]></AName>
<Step rID="T47">
<Obj plainTxt="False" ><![CDATA[Flight Reservation]]></Obj>
<Details plainTxt="False" ><![CDATA[Window]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<Step rID="T48">
<Obj plainTxt="False" ><![CDATA[Menu.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["File;Exit"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:36:13]]></Time>
<NodeArgs eType="Replay" icon="13" nRep="84" >
<Disp><![CDATA[Menu.Select]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="8" nRep="83" >
<Disp><![CDATA[Flight Reservation]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 9:36:13" eTime="2024/6/18/周二 - 9:36:14" passed="0" failed="0" warnings="0" ></Summary>
<NodeArgs eType="StartAction" icon="4" nRep="81" >
<Disp><![CDATA[ExitAction Summary]]></Disp>
</NodeArgs>
</Action>
<Summary sTime="2024/6/18/周二 - 9:35:48" eTime="2024/6/18/周二 - 9:36:14" passed="0" failed="0" warnings="0" ></Summary>
<NodeArgs eType="StartAction" icon="4" nRep="7" >
<Disp><![CDATA[MainAction Summary]]></Disp>
</NodeArgs>
</Action>
<NodeArgs eType="StartIteration" icon="3" nRep="6" >
<Disp><![CDATA[Test1 Iteration 1 (Row 1)]]></Disp>
</NodeArgs>
</DIter>
<Summary sTime="2024/6/18/周二 - 9:35:48" eTime="2024/6/18/周二 - 9:36:14" passed="0" failed="0" warnings="0" ></Summary>
<NodeArgs eType="StartTest" icon="1" nRep="3" >
<Disp><![CDATA[Test Test1 Summary]]></Disp>
</NodeArgs>
</Doc>
</Report>