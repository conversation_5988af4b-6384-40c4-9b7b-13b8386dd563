<?xml version="1.0"?>
<!DOCTYPE Report
[
<!ELEMENT Report (General ,(Doc|BPT)) >
<!ATTLIST Report ver CDATA #REQUIRED tmZone CDATA #REQUIRED>

<!ELEMENT General ( DocLocation ) >
<!ATTLIST General productName CDATA #REQUIRED productVer CDATA #REQUIRED os CDATA #REQUIRED host CDATA #REQUIRED>

<!ELEMENT BPT (DName,Res,DVer?,TSet?,TInst?,NodeArgs,AdditionalInfo*,Doc*) >
<!ATTLIST BPT rID ID #REQUIRED >

<!ELEMENT Doc (DName,Res,DVer?,TSet?,TInst?,RunType?,DT?,AdditionalInfo*,Step*,DIter*,Step*,Action*,Summary?,TestMaintenanceSummary*,NodeArgs?) >
<!ATTLIST Doc rID ID #REQUIRED type (Test|BC) "Test" productName CDATA #REQUIRED BCIter CDATA #IMPLIED >

<!ELEMENT RunType ( #PCDATA )>
<!ATTLIST RunType fmStep (False|True) "False" batch (False|True) "False" upDesc (False|True) "False" upChk (False|True) "False" upAS (False|True) "False">

<!ELEMENT DName ( #PCDATA ) >

<!ELEMENT Res ( #PCDATA ) >

<!ELEMENT AdditionalInfo (AdditionalDataName,AdditionalDataValue ) >

<!ELEMENT AdditionalDataName ( #PCDATA ) >

<!ELEMENT AdditionalDataValue ( #PCDATA ) >

<!ELEMENT DVer ( #PCDATA ) >

<!ELEMENT TSet ( #PCDATA ) >

<!ELEMENT TInst ( #PCDATA ) >

<!ELEMENT DIter (Step?,Action+,Summary?,NodeArgs)>
<!ATTLIST DIter rID ID #REQUIRED iterID CDATA #REQUIRED>

<!ELEMENT DocLocation ( #PCDATA )>

<!ELEMENT Action (AName,AIter*,(Step|HtmlStep|Action)*,Summary,ActionMaintenanceSummary*,NodeArgs ) >
<!ATTLIST Action rID ID #REQUIRED>

<!ELEMENT AIter ((Step|HtmlStep|Action)*,Summary?,NodeArgs) >
<!ATTLIST AIter rID ID #REQUIRED iterID CDATA #REQUIRED>

<!ELEMENT AName ( #PCDATA ) >

<!ELEMENT TestMaintenanceSummary (ActionMaintenanceSummary) >
<!ATTLIST TestMaintenanceSummary ObjectsAdded CDATA #REQUIRED ObjectsUpdated CDATA #REQUIRED StepsUpdated CDATA #REQUIRED StepsComments CDATA #REQUIRED><!ELEMENT ActionMaintenanceSummary (ObjectChange* ) >
<!ATTLIST ActionMaintenanceSummary Action CDATA #REQUIRED Objects CDATA #REQUIRED Updated CDATA #REQUIRED LinesTotal CDATA #REQUIRED Added CDATA #REQUIRED LinesUpdated CDATA #REQUIRED>

<!ELEMENT ObjectChange  (Hierarchy, PropertyChangeList* ) >
<!ATTLIST ObjectChange Operation CDATA #IMPLIED OriginalRepository CDATA #IMPLIED>

<!ELEMENT PropertyChangeList (PropertyDef) >

<!ELEMENT PropertyDef (OriginalValue, NewValue) >
<!ATTLIST PropertyDef PropName CDATA #REQUIRED OriginRegularExpression (True|False) "False" NewRegularExpression (True|False) "False" >

<!ELEMENT OriginalValue ( #PCDATA )>

<!ELEMENT NewValue ( #PCDATA )>

<!ELEMENT Hierarchy  (ObjectName, Hierarchy*) >
<!ATTLIST Hierarchy MicClass CDATA #REQUIRED>

<!ELEMENT ObjectName  ( #PCDATA ) >

<!ELEMENT Step (Obj,Details,Time,(Step|HtmlStep|Doc)*,NodeArgs) >
<!ATTLIST Step rID ID #REQUIRED retval CDATA #IMPLIED>

<!ELEMENT HtmlStep (HTML,(Step|HtmlStep|Doc)*,NodeArgs) >
<!ATTLIST HtmlStep rID ID #REQUIRED >

<!ELEMENT Obj ( #PCDATA ) >
<!ATTLIST Obj plainTxt (False|True) "True">

<!ELEMENT Details ( #PCDATA ) >
<!ATTLIST Details plainTxt (False|True) "True">

<!ELEMENT Time ( #PCDATA ) >

<!ELEMENT HTML ( #PCDATA ) >

<!ELEMENT Disp ( #PCDATA ) >

<!ELEMENT NodeArgs (Disp,TopPane?,BtmPane?)>
<!ATTLIST NodeArgs eType CDATA #REQUIRED icon CDATA #REQUIRED nRep CDATA #REQUIRED filter (False|True) "True">
<!ATTLIST NodeArgs status (Passed|Failed|Done|Warning|Information) "Done">
<!ATTLIST NodeArgs iconSel CDATA #IMPLIED nType CDATA #IMPLIED MovieMarker CDATA "">

<!ELEMENT TopPane (Path)>

<!ELEMENT BtmPane ( (Path|WR)?,ASHilite?)>
<!ATTLIST BtmPane vType CDATA "HTML">

<!ELEMENT Path ( #PCDATA ) >

<!ELEMENT ASHilite ( #PCDATA ) >

<!ELEMENT WR ( #PCDATA ) >

<!ELEMENT DT (NodeArgs) >
<!ATTLIST DT rID ID #REQUIRED>

<!ELEMENT Summary (Param*)>
<!ATTLIST Summary sTime CDATA #IMPLIED eTime CDATA #IMPLIED passed CDATA #IMPLIED failed CDATA #IMPLIED warnings CDATA #IMPLIED retval CDATA #IMPLIED stopped (False|True) "False" >

<!ELEMENT Param (ParamName,ParamVal)+ >
<!ATTLIST Param paramInOut (In|Out) "In">

<!ELEMENT ParamName ( #PCDATA ) >

<!ELEMENT ParamVal ( #PCDATA ) >

]
>
<Report ver="2.0" tmZone="中国标准时间">
<General productName="QuickTest Professional" productVer="9.5" os="" host="PC-038"><DocLocation><![CDATA[C:\Program Files (x86)\HP\QuickTest Professional\Tests\Test1]]></DocLocation></General>
<Doc rID="T1"   productName= "QuickTest Professional"  >
<DName><![CDATA[Test1]]></DName>
<Res><![CDATA[Res13]]></Res>
<DT rID="T2">
<NodeArgs eType="Table" icon="2" nRep="4" filter="False" >
<Disp><![CDATA[Run-Time Data Table]]></Disp>
<BtmPane vType="Table">
<Path><![CDATA[Default.xls]]></Path>
</BtmPane>
</NodeArgs>
</DT>
<DIter rID="T3" iterID="1" >
<Action rID="T4">
<AName><![CDATA[MainAction]]></AName>
<Action rID="T5">
<AName><![CDATA[LoginAction]]></AName>
<Step rID="T6">
<Obj plainTxt="False" ><![CDATA[Login]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 10:19:24]]></Time>
<Step rID="T7">
<Obj plainTxt="False" ><![CDATA[Agent Name:.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 10:19:24]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="13" status="Warning" >
<Disp><![CDATA[Agent Name:.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T8">
<Obj plainTxt="False" ><![CDATA[Agent Name:]]></Obj>
<Details plainTxt="False" ><![CDATA[Cannot find the "Agent Name:" object's parent "Login" (class Dialog). Verify that parent properties match an object currently displayed in your application.]]></Details>
<Time><![CDATA[2024/6/18/周二 - 10:19:24]]></Time>
<Step rID="T9">
<Obj plainTxt="False" ><![CDATA[Login]]></Obj>
<Details plainTxt="False" ><![CDATA[Object's physical description:<br>Text = Login<br>simclass = #32770<br>Native Class = #32770<br>is owned window = 0<br>is child window = 0<br>]]></Details>
<Time><![CDATA[2024/6/18/周二 - 10:19:24]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="15" status="Warning" nType="Parent description" >
<Disp><![CDATA[Login]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Replay" icon="6" nRep="14" status="Failed" nType="Run Error" >
<Disp><![CDATA[Agent Name:]]></Disp>
</NodeArgs>
</Step>
<Step rID="T10">
<Obj plainTxt="False" ><![CDATA[Stop Run]]></Obj>
<Details plainTxt="False" ><![CDATA[Run stopped by user.]]></Details>
<Time><![CDATA[2024/6/18/周二 - 10:19:24]]></Time>
<NodeArgs eType="General" icon="8" nRep="16" >
<Disp><![CDATA[Stop Run]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="12" status="Failed" >
<Disp><![CDATA[Login]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 10:19:21" eTime="2024/6/18/周二 - 10:19:24" passed="0" failed="1" warnings="2" ><Param ><ParamName><![CDATA[UserName]]></ParamName><ParamVal><![CDATA[user01]]></ParamVal>
</Param>
</Summary>
<NodeArgs eType="StartAction" icon="4" nRep="8" status="Failed" >
<Disp><![CDATA[LoginAction Summary]]></Disp>
</NodeArgs>
</Action>
<Step rID="T11">
<Obj plainTxt="False" ><![CDATA[Stop Run]]></Obj>
<Details plainTxt="False" ><![CDATA[Run stopped by user.]]></Details>
<Time><![CDATA[2024/6/18/周二 - 10:19:24]]></Time>
<NodeArgs eType="General" icon="8" nRep="18" >
<Disp><![CDATA[Stop Run]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 10:19:21" eTime="2024/6/18/周二 - 10:19:24" passed="0" failed="0" warnings="0" ></Summary>
<NodeArgs eType="StartAction" icon="4" nRep="7" status="Failed" >
<Disp><![CDATA[MainAction Summary]]></Disp>
</NodeArgs>
</Action>
<NodeArgs eType="StartIteration" icon="3" nRep="6" status="Failed" >
<Disp><![CDATA[Test1 Iteration 1 (Row 1)]]></Disp>
</NodeArgs>
</DIter>
<Step rID="T12">
<Obj plainTxt="False" ><![CDATA[Stop Run]]></Obj>
<Details plainTxt="False" ><![CDATA[Run stopped by user.]]></Details>
<Time><![CDATA[2024/6/18/周二 - 10:19:25]]></Time>
<NodeArgs eType="General" icon="9" nRep="21" >
<Disp><![CDATA[Stop Run]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 10:19:21" eTime="2024/6/18/周二 - 10:19:25" passed="0" failed="1" warnings="2" stopped="True" ></Summary>
<NodeArgs eType="StartTest" icon="1" nRep="3" status="Failed" >
<Disp><![CDATA[Test Test1 Summary]]></Disp>
</NodeArgs>
</Doc>
</Report>