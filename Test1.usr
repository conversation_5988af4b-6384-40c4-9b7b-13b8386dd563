[General]
Type=Tulip
RunType=ActiveScript
DefaultCfg=default.cfg
DefaultRunLogic=default.usp
ParamRightBrace=>
ParamLeftBrace=<
NewFunctionHeader=1
MinorVersion=0
MajorVersion=6	
DevelopTool=AQT

[Actions]
Action0=Action0\Script.mts
MainAction=Action5\Script.mts
LoginAction=Action1\Script.mts
QuitAction=Action6\Script.mts
FaxAction=Action4\Script.mts
SearchAction=Action3\Script.mts
OrderAction=Action2\Script.mts

[VuserProfiles]
Profiles=Default Profile

[CfgFiles]
Default Profile=default.cfg

[RunLogicFiles]
Default Profile=default.usp

[Rendezvous]

[Transactions]

[ActiveScript]
Lang=none

[TulipInfo]
ProductName=QuickTest Professional
Version=9.5

[TulipAddins]
ActiveX=
Database=
Windows Applications=
TEA=
Visual Basic=
XML=

[ExtraFiles]
Test.tsp=
Default.xls=
Parameters.mtr=
Action0\Script.mts=
Action0\Resource.mtr=
Action0\ObjectRepository.bdb=
Action5\Script.mts=
Action5\Resource.mtr=
Action5\ObjectRepository.bdb=
Action1\Script.mts=
Action1\Resource.mtr=
Action1\ObjectRepository.bdb=
Action6\Script.mts=
Action6\Resource.mtr=
Action6\ObjectRepository.bdb=
Action4\Script.mts=
Action4\Resource.mtr=
Action4\ObjectRepository.bdb=
Action4\AnalogTrackList.dat=
Action3\Script.mts=
Action3\Resource.mtr=
Action3\ObjectRepository.bdb=
Action2\Script.mts=
Action2\Resource.mtr=
Action2\ObjectRepository.bdb=
