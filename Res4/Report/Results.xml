<?xml version="1.0"?>
<!DOCTYPE Report
[
<!ELEMENT Report (General ,(Doc|BPT)) >
<!ATTLIST Report ver CDATA #REQUIRED tmZone CDATA #REQUIRED>

<!ELEMENT General ( DocLocation ) >
<!ATTLIST General productName CDATA #REQUIRED productVer CDATA #REQUIRED os CDATA #REQUIRED host CDATA #REQUIRED>

<!ELEMENT BPT (DName,Res,DVer?,TSet?,TInst?,NodeArgs,AdditionalInfo*,Doc*) >
<!ATTLIST BPT rID ID #REQUIRED >

<!ELEMENT Doc (DName,Res,DVer?,TSet?,TInst?,RunType?,DT?,AdditionalInfo*,Step*,DIter*,Step*,Action*,Summary?,TestMaintenanceSummary*,NodeArgs?) >
<!ATTLIST Doc rID ID #REQUIRED type (Test|BC) "Test" productName CDATA #REQUIRED BCIter CDATA #IMPLIED >

<!ELEMENT RunType ( #PCDATA )>
<!ATTLIST RunType fmStep (False|True) "False" batch (False|True) "False" upDesc (False|True) "False" upChk (False|True) "False" upAS (False|True) "False">

<!ELEMENT DName ( #PCDATA ) >

<!ELEMENT Res ( #PCDATA ) >

<!ELEMENT AdditionalInfo (AdditionalDataName,AdditionalDataValue ) >

<!ELEMENT AdditionalDataName ( #PCDATA ) >

<!ELEMENT AdditionalDataValue ( #PCDATA ) >

<!ELEMENT DVer ( #PCDATA ) >

<!ELEMENT TSet ( #PCDATA ) >

<!ELEMENT TInst ( #PCDATA ) >

<!ELEMENT DIter (Step?,Action+,Summary?,NodeArgs)>
<!ATTLIST DIter rID ID #REQUIRED iterID CDATA #REQUIRED>

<!ELEMENT DocLocation ( #PCDATA )>

<!ELEMENT Action (AName,AIter*,(Step|HtmlStep|Action)*,Summary,ActionMaintenanceSummary*,NodeArgs ) >
<!ATTLIST Action rID ID #REQUIRED>

<!ELEMENT AIter ((Step|HtmlStep|Action)*,Summary?,NodeArgs) >
<!ATTLIST AIter rID ID #REQUIRED iterID CDATA #REQUIRED>

<!ELEMENT AName ( #PCDATA ) >

<!ELEMENT TestMaintenanceSummary (ActionMaintenanceSummary) >
<!ATTLIST TestMaintenanceSummary ObjectsAdded CDATA #REQUIRED ObjectsUpdated CDATA #REQUIRED StepsUpdated CDATA #REQUIRED StepsComments CDATA #REQUIRED><!ELEMENT ActionMaintenanceSummary (ObjectChange* ) >
<!ATTLIST ActionMaintenanceSummary Action CDATA #REQUIRED Objects CDATA #REQUIRED Updated CDATA #REQUIRED LinesTotal CDATA #REQUIRED Added CDATA #REQUIRED LinesUpdated CDATA #REQUIRED>

<!ELEMENT ObjectChange  (Hierarchy, PropertyChangeList* ) >
<!ATTLIST ObjectChange Operation CDATA #IMPLIED OriginalRepository CDATA #IMPLIED>

<!ELEMENT PropertyChangeList (PropertyDef) >

<!ELEMENT PropertyDef (OriginalValue, NewValue) >
<!ATTLIST PropertyDef PropName CDATA #REQUIRED OriginRegularExpression (True|False) "False" NewRegularExpression (True|False) "False" >

<!ELEMENT OriginalValue ( #PCDATA )>

<!ELEMENT NewValue ( #PCDATA )>

<!ELEMENT Hierarchy  (ObjectName, Hierarchy*) >
<!ATTLIST Hierarchy MicClass CDATA #REQUIRED>

<!ELEMENT ObjectName  ( #PCDATA ) >

<!ELEMENT Step (Obj,Details,Time,(Step|HtmlStep|Doc)*,NodeArgs) >
<!ATTLIST Step rID ID #REQUIRED retval CDATA #IMPLIED>

<!ELEMENT HtmlStep (HTML,(Step|HtmlStep|Doc)*,NodeArgs) >
<!ATTLIST HtmlStep rID ID #REQUIRED >

<!ELEMENT Obj ( #PCDATA ) >
<!ATTLIST Obj plainTxt (False|True) "True">

<!ELEMENT Details ( #PCDATA ) >
<!ATTLIST Details plainTxt (False|True) "True">

<!ELEMENT Time ( #PCDATA ) >

<!ELEMENT HTML ( #PCDATA ) >

<!ELEMENT Disp ( #PCDATA ) >

<!ELEMENT NodeArgs (Disp,TopPane?,BtmPane?)>
<!ATTLIST NodeArgs eType CDATA #REQUIRED icon CDATA #REQUIRED nRep CDATA #REQUIRED filter (False|True) "True">
<!ATTLIST NodeArgs status (Passed|Failed|Done|Warning|Information) "Done">
<!ATTLIST NodeArgs iconSel CDATA #IMPLIED nType CDATA #IMPLIED MovieMarker CDATA "">

<!ELEMENT TopPane (Path)>

<!ELEMENT BtmPane ( (Path|WR)?,ASHilite?)>
<!ATTLIST BtmPane vType CDATA "HTML">

<!ELEMENT Path ( #PCDATA ) >

<!ELEMENT ASHilite ( #PCDATA ) >

<!ELEMENT WR ( #PCDATA ) >

<!ELEMENT DT (NodeArgs) >
<!ATTLIST DT rID ID #REQUIRED>

<!ELEMENT Summary (Param*)>
<!ATTLIST Summary sTime CDATA #IMPLIED eTime CDATA #IMPLIED passed CDATA #IMPLIED failed CDATA #IMPLIED warnings CDATA #IMPLIED retval CDATA #IMPLIED stopped (False|True) "False" >

<!ELEMENT Param (ParamName,ParamVal)+ >
<!ATTLIST Param paramInOut (In|Out) "In">

<!ELEMENT ParamName ( #PCDATA ) >

<!ELEMENT ParamVal ( #PCDATA ) >

]
>
<Report ver="2.0" tmZone="中国标准时间">
<General productName="QuickTest Professional" productVer="9.5" os="" host="PC-038"><DocLocation><![CDATA[C:\Program Files (x86)\HP\QuickTest Professional\Tests\Test1]]></DocLocation></General>
<Doc rID="T1"   productName= "QuickTest Professional"  >
<DName><![CDATA[Test1]]></DName>
<Res><![CDATA[Res4]]></Res>
<DT rID="T2">
<NodeArgs eType="Table" icon="2" nRep="5" filter="False" >
<Disp><![CDATA[Run-Time Data Table]]></Disp>
<BtmPane vType="Table">
<Path><![CDATA[Default.xls]]></Path>
</BtmPane>
</NodeArgs>
</DT>
<DIter rID="T3" iterID="1" >
<Action rID="T4">
<AName><![CDATA[MainAction]]></AName>
<Action rID="T5">
<AName><![CDATA[LoginAction]]></AName>
<Step rID="T6">
<Obj plainTxt="False" ><![CDATA[Login]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:41]]></Time>
<Step rID="T7">
<Obj plainTxt="False" ><![CDATA[Agent Name:.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:41]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="12" >
<Disp><![CDATA[Agent Name:.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T8">
<Obj plainTxt="False" ><![CDATA[Password:.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA["mercury"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:42]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="14" >
<Disp><![CDATA[Password:.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T9">
<Obj plainTxt="False" ><![CDATA[OK.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:42]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="16" >
<Disp><![CDATA[OK.Click]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="11" >
<Disp><![CDATA[Login]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 9:48:40" eTime="2024/6/18/周二 - 9:48:42" passed="0" failed="0" warnings="0" ><Param ><ParamName><![CDATA[UserName]]></ParamName><ParamVal><![CDATA[user01]]></ParamVal>
</Param>
</Summary>
<NodeArgs eType="StartAction" icon="4" nRep="9" >
<Disp><![CDATA[LoginAction Summary]]></Disp>
</NodeArgs>
</Action>
<Action rID="T10">
<AName><![CDATA[OrderAction]]></AName>
<Step rID="T11">
<Obj plainTxt="False" ><![CDATA[Flight Reservation]]></Obj>
<Details plainTxt="False" ><![CDATA[Window]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<Step rID="T12">
<Obj plainTxt="False" ><![CDATA[MaskEdBox.Type]]></Obj>
<Details plainTxt="False" ><![CDATA["121224"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="9" nRep="21" >
<Disp><![CDATA[MaskEdBox.Type]]></Disp>
</NodeArgs>
</Step>
<Step rID="T13">
<Obj plainTxt="False" ><![CDATA[Fly From:.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["Frankfurt"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="10" nRep="23" >
<Disp><![CDATA[Fly From:.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T14">
<Obj plainTxt="False" ><![CDATA[Fly To:.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["Paris"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="10" nRep="25" >
<Disp><![CDATA[Fly To:.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T15">
<Obj plainTxt="False" ><![CDATA[FLIGHT.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="27" >
<Disp><![CDATA[FLIGHT.Click]]></Disp>
</NodeArgs>
</Step>
<Step rID="T16">
<Obj plainTxt="False" ><![CDATA[Flights Table]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<Step rID="T17">
<Obj plainTxt="False" ><![CDATA[From.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["11278   FRA   05:58 PM   PAR   07:53 PM   AA     $174.00"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="11" nRep="30" >
<Disp><![CDATA[From.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T18">
<Obj plainTxt="False" ><![CDATA[OK.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="32" >
<Disp><![CDATA[OK.Click]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="29" >
<Disp><![CDATA[Flights Table]]></Disp>
</NodeArgs>
</Step>
<Step rID="T19">
<Obj plainTxt="False" ><![CDATA[Name:.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA["nzy"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="34" >
<Disp><![CDATA[Name:.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T20">
<Obj plainTxt="False" ><![CDATA[First.Set]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="12" nRep="36" >
<Disp><![CDATA[First.Set]]></Disp>
</NodeArgs>
</Step>
<Step rID="T21">
<Obj plainTxt="False" ><![CDATA[Tickets:.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA["10"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="38" >
<Disp><![CDATA[Tickets:.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T22">
<Obj plainTxt="False" ><![CDATA[Insert Order.Click]]></Obj>
<Details plainTxt="False" ><![CDATA[]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="7" nRep="40" >
<Disp><![CDATA[Insert Order.Click]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="8" nRep="20" >
<Disp><![CDATA[Flight Reservation]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 9:48:42" eTime="2024/6/18/周二 - 9:48:45" passed="0" failed="0" warnings="0" ></Summary>
<NodeArgs eType="StartAction" icon="4" nRep="18" >
<Disp><![CDATA[OrderAction Summary]]></Disp>
</NodeArgs>
</Action>
<Action rID="T23">
<AName><![CDATA[SearchAction]]></AName>
<Step rID="T24">
<Obj plainTxt="False" ><![CDATA[Flight Reservation]]></Obj>
<Details plainTxt="False" ><![CDATA[Window]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<Step rID="T25">
<Obj plainTxt="False" ><![CDATA[Menu.Select]]></Obj>
<Details plainTxt="False" ><![CDATA["File;Open Order..."]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:45]]></Time>
<NodeArgs eType="Replay" icon="13" nRep="45" >
<Disp><![CDATA[Menu.Select]]></Disp>
</NodeArgs>
</Step>
<Step rID="T26">
<Obj plainTxt="False" ><![CDATA[Open Order]]></Obj>
<Details plainTxt="False" ><![CDATA[Dialog]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:52]]></Time>
<Step rID="T27">
<Obj plainTxt="False" ><![CDATA[Customer Name.Set]]></Obj>
<Details plainTxt="False" ><![CDATA["ON"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:48:52]]></Time>
<NodeArgs eType="Replay" icon="14" nRep="48" >
<Disp><![CDATA[Customer Name.Set]]></Disp>
</NodeArgs>
</Step>
<Step rID="T28">
<Obj plainTxt="False" ><![CDATA[Edit.SetText]]></Obj>
<Details plainTxt="False" ><![CDATA["nzy"]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:49:03]]></Time>
<NodeArgs eType="Replay" icon="6" nRep="50" status="Warning" >
<Disp><![CDATA[Edit.SetText]]></Disp>
</NodeArgs>
</Step>
<Step rID="T29">
<Obj plainTxt="False" ><![CDATA[Edit]]></Obj>
<Details plainTxt="False" ><![CDATA[Object is disabled]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:49:03]]></Time>
<Step rID="T30">
<Obj plainTxt="False" ><![CDATA[Edit]]></Obj>
<Details plainTxt="False" ><![CDATA[Object's physical description:<br>window id = 1014<br>simclass = Edit<br>Native Class = Edit<br>]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:49:03]]></Time>
<NodeArgs eType="Replay" icon="15" nRep="52" status="Warning" nType="Description" >
<Disp><![CDATA[Edit]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Replay" icon="6" nRep="51" status="Failed" nType="Run Error" >
<Disp><![CDATA[Edit]]></Disp>
</NodeArgs>
</Step>
<Step rID="T31">
<Obj plainTxt="False" ><![CDATA[Stop Run]]></Obj>
<Details plainTxt="False" ><![CDATA[Run stopped by user.]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:49:03]]></Time>
<NodeArgs eType="General" icon="16" nRep="53" >
<Disp><![CDATA[Stop Run]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="5" nRep="47" status="Failed" >
<Disp><![CDATA[Open Order]]></Disp>
</NodeArgs>
</Step>
<NodeArgs eType="Context" icon="8" nRep="44" status="Failed" >
<Disp><![CDATA[Flight Reservation]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 9:48:45" eTime="2024/6/18/周二 - 9:49:03" passed="0" failed="1" warnings="2" ></Summary>
<NodeArgs eType="StartAction" icon="4" nRep="42" status="Failed" >
<Disp><![CDATA[SearchAction Summary]]></Disp>
</NodeArgs>
</Action>
<Step rID="T32">
<Obj plainTxt="False" ><![CDATA[Stop Run]]></Obj>
<Details plainTxt="False" ><![CDATA[Run stopped by user.]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:49:03]]></Time>
<NodeArgs eType="General" icon="16" nRep="55" >
<Disp><![CDATA[Stop Run]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 9:48:39" eTime="2024/6/18/周二 - 9:49:03" passed="0" failed="0" warnings="0" ></Summary>
<NodeArgs eType="StartAction" icon="4" nRep="8" status="Failed" >
<Disp><![CDATA[MainAction Summary]]></Disp>
</NodeArgs>
</Action>
<NodeArgs eType="StartIteration" icon="3" nRep="7" status="Failed" >
<Disp><![CDATA[Test1 Iteration 1 (Row 1)]]></Disp>
</NodeArgs>
</DIter>
<Step rID="T33">
<Obj plainTxt="False" ><![CDATA[Stop Run]]></Obj>
<Details plainTxt="False" ><![CDATA[Run stopped by user.]]></Details>
<Time><![CDATA[2024/6/18/周二 - 9:49:03]]></Time>
<NodeArgs eType="General" icon="17" nRep="58" >
<Disp><![CDATA[Stop Run]]></Disp>
</NodeArgs>
</Step>
<Summary sTime="2024/6/18/周二 - 9:48:39" eTime="2024/6/18/周二 - 9:49:03" passed="0" failed="1" warnings="2" stopped="True" ></Summary>
<NodeArgs eType="StartTest" icon="1" nRep="4" status="Failed" >
<Disp><![CDATA[Test Test1 Summary]]></Disp>
</NodeArgs>
</Doc>
</Report>