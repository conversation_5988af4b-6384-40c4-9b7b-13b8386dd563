''按姓名查找
'Window("Flight Reservation").WinMenu("Menu").Select "File;Open Order..."
'Window("Flight Reservation").Dialog("Open Order").WinCheckBox("Customer Name").Set "ON"
'Window("Flight Reservation").Dialog("Open Order").WinEdit("Edit").Set DataTable("customer", dtGlobalSheet)
'Window("Flight Reservation").Dialog("Open Order").WinButton("OK").Click
'Window("Flight Reservation").Dialog("Open Order").Dialog("Search Results").WinList("Flight No.").Click 168,1
'Window("Flight Reservation").Dialog("Open Order").Dialog("Search Results").WinButton("OK").Click


'按日期查找
'Window("Flight Reservation").WinMenu("Menu").Select "File;Open Order..."
'Window("Flight Reservation").Dialog("Open Order").WinCheckBox("Flight Date").Set "ON"
'Window("Flight Reservation").Dialog("Open Order").ActiveX("MaskEdBox").Type DataTable("ticketsdate", dtGlobalSheet)
'Window("Flight Reservation").Dialog("Open Order").WinButton("OK").Click
'Window("Flight Reservation").Dialog("Open Order").Dialog("Search Results").WinList("Flight No.").Select
 '"nzy                     11        AA        11278         1   10 174.000012/12/2024     Tuesday  FRA                     Frankfurt  05:58 PM  PAR                         Paris  07:53 PM"
'Window("Flight Reservation").Dialog("Open Order").Dialog("Search Results").WinButton("OK").Click

'按订单号查找
Window("Flight Reservation").WinMenu("Menu").Select "File;Open Order..."
Window("Flight Reservation").Dialog("Open Order").WinCheckBox("Order No.").Set "ON"
Window("Flight Reservation").Dialog("Open Order").WinEdit("Edit_2").Set DataTable("ordernumber", dtGlobalSheet)
Window("Flight Reservation").Dialog("Open Order").WinButton("OK").Click




